apply plugin: "com.android.application"
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
apply plugin: "com.google.gms.google-services"

/**
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */
react {
    /* Folders */
    //   The root of your project, i.e. where "package.json" lives. Default is '..'
    // root = file("../")
    //   The folder where the react-native NPM package is. Default is ../node_modules/react-native
    // reactNativeDir = file("../node_modules/react-native")
    //   The folder where the react-native Codegen package is. Default is ../node_modules/@react-native/codegen
    // codegenDir = file("../node_modules/@react-native/codegen")
    //   The cli.js file which is the React Native CLI entrypoint. Default is ../node_modules/react-native/cli.js
    // cliFile = file("../node_modules/react-native/cli.js")

    /* Variants */
    //   The list of variants to that are debuggable. For those we're going to
    //   skip the bundling of the JS bundle and the assets. By default is just 'debug'.
    //   If you add flavors like lite, prod, etc. you'll have to list your debuggableVariants.
    // debuggableVariants = ["liteDebug", "prodDebug"]

    /* Bundling */
    //   A list containing the node command and its flags. Default is just 'node'.
    // nodeExecutableAndArgs = ["node"]
    //
    //   The command to run when bundling. By default is 'bundle'
    // bundleCommand = "ram-bundle"
    //
    //   The path to the CLI configuration file. Default is empty.
    // bundleConfig = file(../rn-cli.config.js)
    //
    //   The name of the generated asset file containing your JS bundle
    // bundleAssetName = "MyApplication.android.bundle"
    //
    //   The entry file for bundle generation. Default is 'index.android.js' or 'index.js'
    // entryFile = file("../js/MyApplication.android.js")
    //
    //   A list of extra flags to pass to the 'bundle' commands.
    //   See https://github.com/react-native-community/cli/blob/main/docs/commands.md#bundle
    // extraPackagerArgs = []

    /* Hermes Commands */
    //   The hermes compiler command to run. By default it is 'hermesc'
    // hermesCommand = "$rootDir/my-custom-hermesc/bin/hermesc"
    //
    //   The list of flags to pass to the Hermes compiler. By default is "-O", "-output-source-map"
    // hermesFlags = ["-O", "-output-source-map"]
}

//project.ext.sentryCli = [
//        logLevel: "debug"
//]
//apply from: "../../node_modules/@sentry/react-native/sentry.gradle"

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = false

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
 * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

android {
    compileSdk rootProject.ext.compileSdkVersion
    ndkVersion rootProject.ext.ndkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    namespace "com.camhr.app"
    defaultConfig {
        applicationId "com.camhr.app"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1005031
        versionName "1.5.31"
        manifestPlaceholders = [
                HOST_NAME    : "com.camhr.app",
                JPUSH_PKGNAME: "com.camhr.app",
                JPUSH_APPKEY : "d8dd3a02edf39f4ed42fc715",
                // JPUSH_APPKEY : "189057fe4f71ef86b21ab544",
                JPUSH_CHANNEL: "developer-default",
        ]

        vectorDrawables.useSupportLibrary = true
        multiDexEnabled true
        missingDimensionStrategy 'react-native-camera', 'general'
        resConfigs "en", "zh", "km", "ko", "th", "vi"
    }

    signingConfigs {
        release {
            storeFile file('release.keystore')
            storePassword 'r2HU&GGp'
            keyAlias 'camhr'
            keyPassword 'r2HU&GGp'
        }
    }

    bundle {
        language {
            // Specifies that the app bundle should not support
            // configuration APKs for language resources. These
            // resources are instead packaged with each base and
            // feature APK.
            enableSplit = false
        }
        density {
            // This property is set to true by default.
            enableSplit = true
        }
        abi {
            // This property is set to true by default.
            enableSplit = true
        }
    }

    buildTypes {
        release {
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            signingConfig signingConfigs.release
        }
        debug {
            signingConfig signingConfigs.release
        }
    }

    flavorDimensions "environment", "channel", "abi"
    productFlavors {
        dev {
            dimension "environment"
        }
        prod {
            dimension "environment"
            versionCode 8000000 + android.defaultConfig.versionCode
        }

        google {
            dimension "channel"
        }
        common {
            dimension "channel"
        }

        arm {
            dimension "abi"
            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a"
            }
        }
        x86 {
            dimension "abi"
            ndk {
                abiFilters "x86", "x86_64"
            }
        }
        allAbi {
            dimension "abi"
            ndk {
                abiFilters "armeabi-v7a", "x86", "arm64-v8a", "x86_64"
            }
        }
    }
    packagingOptions {
        exclude 'META-INF/androidx.exifinterface_exifinterface.version'

        jniLibs {
            useLegacyPackaging true // 压缩so文件，不推荐的
        }
    }
}

def appName = "Camhr"
android.applicationVariants.all { variant ->
    def env = variant.productFlavors[0].name
    def channel = ""
    def abi = variant.productFlavors[2].name
    if (abi == "arm") {
        abi = ""
    } else {
        abi = "-${abi}"
    }
    def buildTypeName = variant.buildType.name
    if (buildTypeName == "release") {
        variant.outputs.all {
            //修改apk文件名
            outputFileName = "${appName}-${env}${channel}${abi}-${variant.versionName}-${variant.versionCode}.apk"
        }
    } else {
        variant.outputs.all {
            //修改apk文件名
            outputFileName = "${appName}-${env}${channel}${abi}-${buildTypeName}-${variant.versionName}-${variant.versionCode}.apk"
        }
    }
}

dependencies {
    implementation project(':jcore-react-native')
    implementation project(':jpush-react-native')
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation("com.facebook.react:react-android")

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }

    // From node_modules
    implementation 'com.facebook.fresco:animated-gif:1.10.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

    // 接入 FCM 厂商
    def jPushPlugin = '5.0.4'
    implementation "cn.jiguang.sdk.plugin:fcm:$jPushPlugin"
    implementation platform('com.google.firebase:firebase-bom:30.1.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-messaging'
    implementation 'com.facebook.android:facebook-android-sdk:4.42.0'

    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"

    implementation 'com.blankj:utilcodex:1.31.1'
    implementation 'com.android.support:multidex:1.0.3'

}

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
