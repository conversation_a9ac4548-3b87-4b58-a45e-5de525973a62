import React, { Component } from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { inject, observer } from 'mobx-react';
import BottomModal from './bottomModal';
import { Button } from '../index';

import styles from '../../themes/enterprise';
import resIcon from '../../res';
import PageFlatList from '../list/pageFlatList';
import { hasEar } from '../../common';
import I18n from '../../i18n';

function getComponentStyle(theme) {
  return {
    pagerContainer: {
      backgroundColor: theme.minorBgColor,
      flex: 1,
    },
    itemBox: {
      backgroundColor: theme.primaryBgColor,
      borderRadius: 5,
      marginHorizontal: 12,
      marginTop: 10,
      overflow: 'hidden',
      paddingVertical: 16,
      paddingHorizontal: 14,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    itemBoxSelected: {
      backgroundColor: '#E8F4FD',
      borderColor: theme.stressColor,
      borderWidth: 1,
    },
    itemContainer: {
      flex: 1,
      flexShrink: 1,
    },
    nameText: {
      fontSize: 17,
      fontWeight: theme.fontWeightBold,
      color: theme.titleFontColor,
    },
    phoneText: {
      fontSize: theme.fontSizeS,
      color: theme.mediumFontColor,
      marginTop: 4,
    },
    emailText: {
      fontSize: theme.fontSizeS,
      color: theme.subFontColor,
    },
    selectIcon: {
      width: 20,
      height: 20,
      marginLeft: 10,
    },
    buttonContainer: {
      paddingHorizontal: 20,
      paddingVertical: 15,
      paddingBottom: hasEar ? 40 : 15,
      backgroundColor: theme.primaryBgColor,
    },
    confirmButton: {
      width: '100%',
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 50,
    },
    emptyText: {
      fontSize: theme.fontSizeM,
      color: theme.subFontColor,
    },
  };
}

/**
 * 选择联系人弹出框组件
 * <AUTHOR>
 */
@inject('stores')
@observer
export default class SelectContactModal extends Component {
  constructor(props) {
    super(props);
    this.style = getComponentStyle(styles.get('theme'));
    this.state = {
      showModal: false,
      selectedContact: null,
      contacts: [],
    };
  }

  show = (data) => {
    this.setState({ showModal: true });
    this.loadContacts();
  };

  close = () => {
    this.setState({ showModal: false, selectedContact: null });
  };

  loadContacts = async () => {
    try {
      // 从companyStore获取联系人列表
      const { companyStore } = this.props.stores;
      const contacts = companyStore.imAccounts || [];
      console.log('加载联系人:', contacts);
      this.setState({ contacts });
    } catch (error) {
      console.error('加载联系人失败:', error);
      this.setState({ contacts: [] });
    }
  };

  onSelectContact = (contact) => {
    this.setState({ selectedContact: contact });
  };

  onConfirm = () => {
    const { selectedContact } = this.state;
    if (this.props.onConfirm) {
      this.props.onConfirm(selectedContact);
    }
    this.close();
  };

  renderItem = ({ item, index }) => {
    const { style } = this;
    const { selectedContact } = this.state;
    const isSelected = selectedContact && selectedContact.contactId === item.contactId;

    return (
      <TouchableOpacity
        style={[style.itemBox, isSelected && style.itemBoxSelected]}
        key={index}
        onPress={() => this.onSelectContact(item)}
      >
        <View style={style.itemContainer}>
          <Text style={style.nameText}>{item.nickname || ''}</Text>
          {item.imUsername && (
            <Text style={style.phoneText}>
              {I18n.t('page_chat_text_im_account')}: {item.imUsername}
            </Text>
          )}
        </View>
        {isSelected && (
          <Image
            source={resIcon.resumeCheckedEnterprise}
            style={style.selectIcon}
            resizeMode="contain"
          />
        )}
      </TouchableOpacity>
    );
  };

  render() {
    const { showModal, contacts, selectedContact } = this.state;
    const { style } = this;

    return (
      <BottomModal
        ref={(ref) => (this.modal = ref)}
        backdropPressToClose
        title={I18n.t('page_company_text_select_contact')}
        showCancel={false}
        rightComponent={<Image source={resIcon.jobModalCloseEnterprise} />}
        rightAction={this.close}
        contentHeight={460}
        keyboardShouldPersistTaps="always"
        onClosed={this.close}
        isOpen={showModal}
        useScrollContent={false}
        showBottomView={false}
      >
        <View style={style.pagerContainer}>
          <PageFlatList
            data={contacts}
            renderItem={this.renderItem}
            showsVerticalScrollIndicator={false}
            keyExtractor={(item, index) => `contact_${item.contactId || index}`}
          />
        </View>

        {/* 确认按钮 */}
        <View style={style.buttonContainer}>
          <Button
            title={I18n.t('page_chat_btn_confirm_select')}
            onPress={this.onConfirm}
            btnSize="lg"
            btnType="primary"
            containerStyle={style.confirmButton}
            disabled={!selectedContact}
          />
        </View>
      </BottomModal>
    );
  }
}
