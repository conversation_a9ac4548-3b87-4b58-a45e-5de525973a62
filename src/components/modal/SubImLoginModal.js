import React, { Component } from 'react';
import { View, Text, TouchableOpacity, TextInput, Keyboard } from 'react-native';
import { inject, observer } from 'mobx-react';
import BottomModal from './bottomModal';
import { Button, KeyboardAwareScrollView } from '../index';

import styles from '../../themes/enterprise';
import SelectContactModal from './SelectContactModal';
import chatAction from '../../store/actions/chatAction';
import uiUtil from '../../util/uiUtil';
import Md5 from 'crypto-js/md5';
import { setAdjustResize } from '../../components/softInputMode';
import I18n from '../../i18n';

function getComponentStyle(theme) {
  return {
    container: {
      paddingHorizontal: 20,
      paddingBottom: 15,
    },
    inputRow: {
      marginBottom: 20,
    },
    desc: {
      fontSize: theme.fontSizeS,
      color: theme.mediumFontColor,
      marginBottom: 10,
      lineHeight: 20,
      textAlign: 'left',
      marginTop: 5,
    },
    label: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      marginBottom: 8,
      fontWeight: theme.fontWeightMedium,
    },
    input: {
      height: 44,
      borderWidth: 1,
      borderColor: '#E5E5E5',
      borderRadius: 4,
      paddingHorizontal: 12,
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
      backgroundColor: '#fff',
    },
    selectButton: {
      height: 44,
      borderWidth: 1,
      borderColor: '#E5E5E5',
      borderRadius: 4,
      paddingHorizontal: 12,
      justifyContent: 'center',
      backgroundColor: '#fff',
    },
    selectButtonText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
    },
    selectButtonPlaceholder: {
      fontSize: theme.fontSizeM,
      color: '#999',
    },
    radioRow: {
      marginBottom: 20,
    },
    radioGroup: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    radioOption: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 30,
    },
    radioButton: {
      width: 18,
      height: 18,
      borderRadius: 9,
      borderWidth: 2,
      borderColor: '#E5E5E5',
      marginRight: 8,
      justifyContent: 'center',
      alignItems: 'center',
    },
    radioButtonSelected: {
      borderColor: theme.stressColor,
    },
    radioButtonInner: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: theme.stressColor,
    },
    radioText: {
      fontSize: theme.fontSizeM,
      color: theme.titleFontColor,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 10,
    },
    button: {
      flex: 1,
      marginHorizontal: 5,
    },
  };
}

/**
 * 登录IM子账号弹出框组件
 * <AUTHOR>
 */
@inject('stores')
@observer
export default class SubImLoginModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isVisible: false,
      selectedContact: null,
      password: '',
      isFocus: false,
    };
  }

  show = (data = {}) => {
    // 如果没有传入选中的联系人，尝试从store获取当前选中的账号
    const { companyStore } = this.props.stores;
    const defaultContact = data.selectedContact || companyStore.selectedImAccount;

    this.setState({
      isVisible: true,
      selectedContact: defaultContact,
      password: data.password || '',
    });
  };

  hide = () => {
    this.setState({ isVisible: false });
  };

  onSelectContact = () => {
    Keyboard.dismiss();
    this.selectContactModal.wrappedInstance.show(this.state.selectedContact);
  };

  onContactSelected = (contact) => {
    this.setState({ selectedContact: contact });
  };

  onChangePassword = (text) => {
    this.setState({ password: text });
  };

  onConfirm = async () => {
    try {
      const { selectedContact, password } = this.state;

      // 验证选择的联系人
      if (!selectedContact) {
        global.toast.show(I18n.t('page_chat_tips_select_contact'));
        return;
      }

      // 验证密码
      if (!password.trim()) {
        global.toast.show(I18n.t('page_chat_ph_input_chat_password'));
        return;
      }
      uiUtil.showGlobalLoading();
      const imPassword = Md5(password.trim()).toString();
      await chatAction.loginIMByEmployee({
        imUsername: selectedContact.imUsername,
        imPassword,
      });

      const { companyStore } = this.props.stores;
      await companyStore.setSelectedImAccount({ ...selectedContact, imPassword });
      uiUtil.hideGlobalLoading();
      this.hide();
    } catch (error) {
      uiUtil.showRequestResult(error);
    }
  };

  onCancel = () => {
    chatAction.loginIM();
    this.hide();
  };

  onFocus = () => {
    console.log('messageInput onFocus');
    setAdjustResize();
    this.setState({ isFocus: true });
  };

  onBlur = () => this.setState({ isFocus: false });

  render() {
    const { isVisible, selectedContact, password } = this.state;
    const { themeStyle } = styles.get(['theme']);
    const style = getComponentStyle(themeStyle);
    const contentHeight = 250;

    return (
      <>
        <BottomModal
          ref={(ref) => (this.modal = ref)}
          backdropPressToClose
          title={I18n.t('page_chat_label_login_child_im')}
          showCancel={false}
          contentHeight={contentHeight}
          keyboardShouldPersistTaps="handled"
          onClosed={this.hide}
          isOpen={isVisible}
          showBottomView={false}
          useScrollContent
          coverScreen
        >
          <View style={style.container}>
            {/* 企业联系人账号选择 */}
            <View style={style.inputRow}>
              <Text style={style.label}>{I18n.t('page_chat_label_company_contact')}:</Text>
              <TouchableOpacity style={style.selectButton} onPress={this.onSelectContact}>
                <Text
                  style={selectedContact ? style.selectButtonText : style.selectButtonPlaceholder}
                >
                  {selectedContact
                    ? selectedContact.imUsername
                    : I18n.t('page_chat_ph_company_contact')}
                </Text>
              </TouchableOpacity>
            </View>

            {/* 密码输入 */}
            <View style={{}}>
              <Text style={style.label}>{I18n.t('page_login_ph_password')}:</Text>
              <TextInput
                ref={(ref) => (this.passwordInput = ref)}
                style={style.input}
                placeholder={I18n.t('page_chat_ph_input_chat_password')}
                value={password}
                onChangeText={this.onChangePassword}
                secureTextEntry={true}
                autoCapitalize="none"
                autoCorrect={false}
                returnKeyType="done"
                selectionColor="#EF3D48"
                onSubmitEditing={this.onConfirm}
                maxLength={31}
                placeholderTextColor="#999"
                onFocus={this.onFocus}
                onBlur={this.onBlur}
                isFocus={this.state.isFocus}
              />
            </View>
            <Text style={style.desc}>{I18n.t('page_chat_desc_set_chat_password')}</Text>

            {/* 按钮组 */}
            <View style={style.buttonContainer}>
              <Button
                title={I18n.t('page_resume_btn_cancel')}
                onPress={this.onCancel}
                btnSize="md"
                outline
                btnType="reset"
                containerStyle={style.button}
              />
              <Button
                title={I18n.t('im_btn_alert_confirm')}
                onPress={this.onConfirm}
                btnSize="md"
                btnType="primary"
                containerStyle={style.button}
              />
            </View>
          </View>
        </BottomModal>

        {/* 选择联系人弹出框 */}
        <SelectContactModal
          ref={(ref) => (this.selectContactModal = ref)}
          onConfirm={this.onContactSelected}
        />
      </>
    );
  }
}
