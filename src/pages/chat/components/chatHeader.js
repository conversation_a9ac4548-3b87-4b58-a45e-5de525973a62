import React, { Component } from 'react';
import { inject, observer } from 'mobx-react';
import constant from '../../../store/constant';
import { Icon, Image, StatusBar, Text, Touchable, View, AlertPro } from '../../../components';
import { TouchableOpacity } from 'react-native';
import I18n from '../../../i18n';
import styles from '../../../themes';
import Avatar from '../../../components/avatar/avatar';
import resIcon from '../../../res';
import PermissionUtil from '../../../util/permissionUtilExtra';
import callAction from '../../../store/actions/callAction';
import navigationService from '../../../navigationService';
import sendMessageUtil from '../../../database/sendMessageUtil';
import companyAction from '../../../store/actions/company';
import uiUtil from '../../../util/uiUtil';

function getStyle(theme) {
  return {
    container: {
      height: 56,
      backgroundColor: '#fff',
      flexDirection: 'row',
      alignItems: 'center',
    },
    backContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingLeft: 10,
      paddingRight: 20,
      height: '100%',
    },
    avatarBox: {
      position: 'relative',
      width: 36,
      height: 36,
      marginRight: 8,
    },
    avatar: {
      width: 36,
      height: 36,
      borderRadius: 14,
    },
    dotStatus: {
      position: 'absolute',
      right: -2,
      bottom: 2,
      width: 10,
      height: 10,
      borderRadius: 4,
      backgroundColor: '#0AB28B',
      borderWidth: 2,
      borderColor: '#fff',
    },
    leftContainer: {
      flex: 1,
      height: '100%',
    },
    infoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      height: '100%',
    },
    titleContainer: {
      flex: 1,
      justifyContent: 'center',
      marginRight: 20,
    },
    title: {
      color: theme.titleFontColor,
      fontSize: theme.fontSizeL,
      fontWeight: theme.fontWeightMedium,
    },
    numText: {
      color: '#99A3BA',
      fontSize: theme.fontSizeS,
      fontWeight: theme.fontWeightMedium,
      marginTop: 4,
    },
    imageButtonTouchable: {
      marginRight: 10,
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    },
    vipTitle: {
      color: '#d6cf00',
      // backgroundColor: '#d6cf00',
      fontSize: 12,
      borderRadius: 10,
    },
    onlineStatus: {
      fontSize: theme.fontSizeS,
      color: '#99A3BA',
      lineHeight: 17,
    },
    companyButtonGroup: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    companyButtonContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      flex: 1,
      paddingBottom: 10,
    },
    companyButtonText: {
      color: '#333',
      fontSize: 12,
    },
    btnText: {
      color: '#333',
      fontSize: 10,
      marginTop: 4,
    },
  };
}

function ImageButton({ style, onPress, img, hide, containerStyle = {}, title, isIcon = false }) {
  if (hide) return null;
  return (
    <Touchable onPress={onPress} style={[style.imageButtonTouchable, containerStyle]}>
      {isIcon ? (
        <Icon type="font-awesome" name="telegram" size={24} color="#333" />
      ) : (
        <Image source={img} resizeMode="contain" />
      )}
      <Text style={style.btnText}>{title}</Text>
    </Touchable>
  );
}

function CompanyButton({ style, onPress, img, i18nKey }) {
  return (
    <Touchable onPress={onPress} style={style.companyButtonContainer}>
      <Image source={img} resizeMode="center" />
      <Text style={style.companyButtonText}>{I18n.t(i18nKey)}</Text>
    </Touchable>
  );
}

@inject('userStore', 'chatStore', 'imAction', 'meetingAction', 'stores')
@observer
export default class ChatHeader extends Component {
  constructor(props) {
    super(props);
    this.style = getStyle(styles.get('theme'));
    this.state = {
      showAlert: false,
    };
  }

  componentDidMount() {}

  componentWillUnmount() {}

  get isSelf() {
    return this.props.session.sessionId === this.props.userStore.imId;
  }

  onVideoCall = async () => {
    try {
      if (callAction.checkInCall()) {
        return;
      }
      await PermissionUtil.requestMultiple();
      await callAction.startCall(constant.callType.singleVideoCall, this.props.session.sessionId);
    } catch (e) {
      console.warn('chatHeader onVideoCall', e);
    }
  };

  onAudioCall = async () => {
    try {
      if (callAction.checkInCall()) {
        return;
      }
      await PermissionUtil.requestAudioPermission();
      await callAction.startCall(constant.callType.singleAudioCall, this.props.session.sessionId);
    } catch (e) {
      console.warn('chatHeader onAudioCall', e);
    }
  };

  // 群聊发起音视频
  onGroupCall = async () => {
    try {
      const { session } = this.props;
      const { groupMembers } = await this.props.imAction.getGroupMemberList(
        session.sessionId,
        false
      );
      if (this.props.meetingAction.checkInCall()) {
        return;
      }
      await PermissionUtil.requestMultiple();

      this.props.meetingAction.startCall(groupMembers);
    } catch (e) {
      logger.warn('chatForwardSelectList onGroupCall', e, message);
      toast.show(e?.message);
    }
  };

  onRequestAudioCall = () => {
    this.setState({ showAlert: true });
  };
  onAudioCallConfirm = async () => {
    this.setState({ showAlert: false });
    this.onAudioCall();
  };

  onAlertCancel = () => {
    this.setState({ showAlert: false });
  };

  goBack = () => {
    navigationService.goBack();
  };

  onProfile = () => {
    const { session, onDetail } = this.props;
    if (session?.isManage) return;
    if (session?.isGroup) {
      onDetail?.();
    } else {
      console.log('onProfile', session);
      navigationService.push('chatAddContactDetail', {
        imId: session?.sessionId,
        deleteRouteName: 'main', // 删除好友后的路由页面
      });
    }
  };

  onResume = () => {
    global.emitter.emit(constant.event.showResumeModal, {
      isOpen: true,
      page: 'chatMessage',
    });
  };

  onSendTG = () => {
    // 使用全局事件触发Telegram弹窗
    global.emitter.emit(constant.event.showTelegramModal, {
      session: this.props.session,
    });
  };

  onReqResume = () => {
    const { session } = this.props;
    sendMessageUtil.sendMessage({
      type: constant.messageType.reqResume,
      sessionId: session.sessionId,
      session,
    });
  };

  onInterview = async () => {
    try {
      const { statistics } = this.props.stores.companyStore;
      if (!statistics?.online) {
        return toast.show(I18n.t('page_resume_text_no_online_job'));
      }
      const { session } = this.props;
      if (!session.resumeId) {
        toast.show(I18n.t('page_chat_no_resume'));
        return;
      }
      uiUtil.showGlobalLoading();
      const resume = await companyAction.getResumeDetail(session.resumeId);
      console.debug('resumeRes', resume);
      if (!resume?.resumeId) {
        uiUtil.showResultMessage(I18n.t('page_resume_text_resume_not_exist'));
        return;
      }
      const res = await companyAction.queryApplicationByJobId({
        jobId: session.jobId || '0',
        resumeId: session.resumeId,
      });
      console.debug('onInterview', res);
      if (!res) {
        toast.show(I18n.t('page_chat_no_job_apply'));
        return;
      }
      console.debug('onInterview companyInfo', this.props.stores.companyStore.companyInfo);
      uiUtil.showRequestResult();
      resume.id = res.id;
      resume.employerId = res.employerId;
      global.emitter.emit(constant.event.showInviteModal, {
        page: 'chatMessage',
        data: resume,
        onConfirm: this.onInterviewConfirm,
        jobId: res.jobId,
      });
    } catch (e) {
      uiUtil.showRequestResult(e);
    }
  };

  onInterviewConfirm = (p, param) => {
    console.debug('onInterviewConfirm', p, param);
    const { session } = this.props;
    session.jobTitle = p.jobTitle;
    session.jobId = p.jobId;
    session.resumeId = p.cvId;
    sendMessageUtil.sendMessage({
      type: constant.messageType.inviteInterview,
      sessionId: session.sessionId,
      session,
      content: JSON.stringify({ jobApplyId: p.id }),
      jobId: p.jobId,
      resumeId: p.cvId,
    });
  };

  renderCompanyButton = (style) => {
    return (
      <View style={style.companyButtonGroup}>
        <CompanyButton
          style={style}
          img={resIcon.chatResume}
          i18nKey="page_chat_req_resume"
          onPress={this.onReqResume}
        />
        <CompanyButton
          style={style}
          img={resIcon.chatAudio}
          i18nKey="page_chat_call_phone"
          onPress={this.onRequestAudioCall}
        />
        <CompanyButton
          style={style}
          img={resIcon.chatHeaderInterviewReq}
          i18nKey="page_resume_text_interview"
          onPress={this.onInterview}
        />
      </View>
    );
  };

  render() {
    const { style } = this;
    const { title, session, onDetail, groupInfo } = this.props;
    const { isCompany } = this.props.userStore;
    const hideVideo = session?.isGroup || session?.isManage || session?.isSelf;
    const desc = this.props.userStore.isCompany ? session.jobTitle : session.company;
    return (
      <>
        <StatusBar backgroundColor="#fff" />
        <View style={style.container}>
          <Touchable onPress={this.goBack} style={style.backContainer}>
            <Image source={resIcon.iconBackDark} />
          </Touchable>
          <View style={style.leftContainer}>
            <Touchable onPress={!this.isSelf && onDetail}>
              <View style={style.infoContainer}>
                {session?.isGroup ? null : (
                  <View style={style.avatarBox}>
                    <Avatar
                      avatar={session?.avatar || null}
                      name={title}
                      style={style.avatar}
                      defaultAvatar={resIcon.chatDefaultAvatar}
                    />
                    {/*<View style={style.dotStatus} />*/}
                  </View>
                )}
                <View style={style.titleContainer}>
                  <Text style={style.title} numberOfLines={1}>
                    {title}
                    {session.positionName && !this.props.userStore.isCompany
                      ? `.${session.positionName}`
                      : ''}
                    {groupInfo && groupInfo.isVip ? (
                      <Text style={style.vipTitle}> VIP </Text>
                    ) : null}
                  </Text>
                  {desc ? (
                    <Text style={style.numText} numberOfLines={1}>
                      {desc}
                    </Text>
                  ) : null}
                  {/*{!session?.isGroup ? (
                    <Text style={style.onlineStatus}>{I18n.t('page_chat_text_online')}</Text>
                  ) : null}*/}
                </View>
              </View>
            </Touchable>
          </View>
          {session.isGPT || isCompany ? null : (
            <ImageButton
              style={style}
              onPress={this.onRequestAudioCall}
              img={resIcon.chatAudio}
              hide={hideVideo}
              title={I18n.t('page_chat_text_online_call')}
            />
          )}
          {session.isGPT || isCompany ? null : (
            <ImageButton
              style={style}
              onPress={this.onSendTG}
              isIcon
              hide={hideVideo}
              title={I18n.t('page_chat_send_tg')}
            />
          )}
          {session.isGPT || isCompany ? null : (
            <ImageButton
              style={style}
              onPress={this.onResume}
              img={resIcon.chatResume}
              hide={hideVideo}
              title={I18n.t('page_chat_text_send_resume')}
            />
          )}
        </View>
        {!session.isGPT && isCompany ? this.renderCompanyButton(style) : null}

        <AlertPro
          visible={this.state.showAlert}
          title={I18n.t('page_chat_tips_network_call')}
          textConfirm={I18n.t('page_setting_confirm_text')}
          textCancel={I18n.t('op_cancel_title')}
          onConfirm={this.onAudioCallConfirm}
          onCancel={this.onAlertCancel}
        />
      </>
    );
  }
}
