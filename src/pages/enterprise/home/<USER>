import React, { Component } from 'react';
import {
  Image,
  ScrollView,
  Text,
  Touchable,
  View,
  LinearGradient,
  Alert,
  RefreshControl,
} from '../../../components';
import { inject, observer } from 'mobx-react';
import styles from '../../../themes/enterprise';
import I18n, { setLanguage } from '../../../i18n';
import NavigationService from '../../../navigationService';
import resIcon from '../../../res';
import TooltipMenu from '../../../components/modal/tooltipMenu';
import I18nUtil from '../../../util/I18nUtil';
import constant from '../../../store/constant';
import util from '../../../util';
import CheckVersionModal from '../../../components/checkVersionModal';
import SubImLoginModal from '../../../components/modal/SubImLoginModal';

/**
 * 首页
 */
@inject(
  'loginAction',
  'companyAction',
  'companyStore',
  'pageAction',
  'globalAction',
  'homeAction',
  'chatAction',
  'stores'
)
@observer
export default class Home extends Component {
  constructor(props) {
    super(props);
    this.homeStyle = styles.get('home');
    this.state = {
      showAlert: false,
      refreshing: false,
    };
  }

  componentDidMount() {
    console.log('Home componentDidMount');
    this.loadData();
    global.emitter.on(constant.event.imConfigChange, this.handleImConfigChange);
    global.emitter.on(constant.event.resumeStatusChanged, this.onResumeStatusChanged);
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.imConfigChange, this.handleImConfigChange);
    global.emitter.off(constant.event.resumeStatusChanged, this.onResumeStatusChanged);
  }

  onResumeStatusChanged = () => {
    setTimeout(async () => {
      await this.props.companyAction.getHomeResumeStatistics();
    }, 600);
  };

  loadData = async () => {
    await this.props.companyAction.getEmployer();
    await this.props.companyAction.getEmployerImConfig();
  };

  handleImConfigChange = async () => {
    // 当IM配置变化时，检查是否需要显示子账号登录弹框
    const { companyStore, userStore } = this.props.stores;
    if (!userStore.isCompany) return;
    if (companyStore.enableSubImAccount) {
      if (!companyStore.selectedImAccount) {
        setTimeout(() => {
          this.subImLoginModal.wrappedInstance.show();
        }, 500);
      } else {
        // 如果已经选择了子账号，直接登录
        const params = {
          imUsername: companyStore.selectedImAccount.imUsername,
          imPassword: companyStore.selectedImAccount.imPassword,
        };
        await this.props.chatAction.loginIMByEmployee(params);
      }
    }
  };

  onRefresh = async () => {
    this.setState({ refreshing: true });
    await this.props.homeAction.getData();
    this.setState({ refreshing: false });
  };

  onChangeAccount = () => {
    NavigationService.navigate('changeAccount');
  };

  onNotify = () => {
    NavigationService.navigate('messageList');
  };

  onSetting = () => {
    NavigationService.navigate('setting');
  };

  onSwitchLanguage = (language) => {
    setLanguage(language);
    // NavigationService.reset('main');
    if (global.IS_IOS) {
      I18nUtil.modifyDefaultLanguage(language, () => {});
    }
    global.emitter.emit('languageChange', true);
    this.forceUpdate();
  };

  onCompany = () => {
    NavigationService.navigate('company');
  };

  onRecharge = () => NavigationService.navigate('companyRecharge', { home: true });

  onJobManagerChange = (item) => {
    this.props.pageAction.selectedTab(constant.tabs.job);
    NavigationService.reset('main', { initialPage: item.type });
  };

  onResume = async (item) => {
    if (item.type == 99) {
      NavigationService.navigate('resumeCollection');
      return;
    }
    await this.props.pageAction.selectedTab(constant.tabs.resume);
    NavigationService.reset('main', { initType: item.type });
    global.emitter.emit(constant.event.homeResumeFilterChanged, { initType: item.type });
  };

  onInterview = (item) => {
    NavigationService.navigate('interviewManagement', { initialPage: item.type });
  };

  renderSectionContent = ({
    customSectionContainer = {},
    headerTitle = '',
    btnImg,
    btnOnPress,
    actions = [],
    onPress,
  }) => {
    const { homeStyle } = this;
    return (
      <View style={[homeStyle.sectionContainer, customSectionContainer]}>
        <View style={homeStyle.sectionHeader}>
          <View style={homeStyle.sectionLeft}>
            <Image source={resIcon.homeLine} />
            <Text style={homeStyle.sectionHeaderTitle}>{headerTitle}</Text>
          </View>
          {btnImg ? (
            <Touchable onPress={btnOnPress}>
              <Image source={btnImg} />
            </Touchable>
          ) : null}
        </View>
        <View style={homeStyle.sectionLine} />
        <View style={homeStyle.sectionContent}>
          {actions?.map((item, index) => (
            <Touchable
              style={[
                homeStyle.sectionContentItem,
                index == actions.length - 1 ? homeStyle.sectionContentItemLast : {},
              ]}
              key={index}
              onPress={() => onPress?.(item)}
            >
              <Text style={homeStyle.sectionContentItemValue}>
                {item.value > 9999 ? '9999+' : item.value}
              </Text>
              <Text style={homeStyle.sectionContentItemLabel}>{item.label}</Text>
            </Touchable>
          ))}
        </View>
      </View>
    );
  };

  renderTopHeader = (homeStyle) => {
    const { companyInfo, messageTotal } = this.props.companyStore;
    return (
      <LinearGradient colors={['#EF3D4890', '#f7f7f7']} style={homeStyle.headerBox}>
        <View style={homeStyle.iconContainer}>
          <Image
            source={resIcon.iconLogoEnterprise}
            style={homeStyle.logoImg}
            resizeMode="contain"
          />
          <View style={homeStyle.logoContainer}>
            <Touchable onPress={this.onChangeAccount} style={homeStyle.msgIconView}>
              <Image source={resIcon.homeUserChange} style={homeStyle.changeImg} />
            </Touchable>
            <View style={[homeStyle.imgSpace]}>
              <Touchable onPress={this.onNotify} style={[homeStyle.msgIconView, { width: 50 }]}>
                <Image source={resIcon.msgEnterprise} style={homeStyle.changeImg} />
                {messageTotal ? (
                  <View style={homeStyle.msgIconNumView}>
                    <Text style={homeStyle.msgIconNum}>
                      {messageTotal > 999 ? '999+' : messageTotal}
                    </Text>
                  </View>
                ) : null}
              </Touchable>
            </View>
            {/* <View style={homeStyle.languageBox}>
              <TooltipMenu
                isModalOpen
                componentWrapperStyle={homeStyle.languageTooltipWrap}
                buttonComponent={
                  <View style={homeStyle.language}>
                    <Text style={homeStyle.languageText}>{I18n.t('language')}</Text>
                  </View>
                }
                items={[
                  {
                    label: I18n.t('language', { locale: 'zh' }),
                    onPress: () => this.onSwitchLanguage('zh'),
                  },
                  {
                    label: I18n.t('language', { locale: 'en' }),
                    onPress: () => this.onSwitchLanguage('en'),
                  },
                  {
                    label: I18n.t('language', { locale: 'km' }),
                    onPress: () => this.onSwitchLanguage('km'),
                  },
                ]}
              />
            </View> */}
            <Touchable onPress={this.onSetting} style={homeStyle.msgIconView}>
              <Image source={resIcon.homeSetting} style={homeStyle.settingImg} />
            </Touchable>
          </View>
        </View>
        <Touchable style={homeStyle.companyContainer} onPress={this.onCompany}>
          <View style={homeStyle.companyLogoContainer}>
            <View style={homeStyle.companyLogoBox}>
              <Image
                source={
                  companyInfo?.logo
                    ? {
                        uri: companyInfo?.logo,
                      }
                    : resIcon.defaultEmployerAvatar
                }
                style={homeStyle.companyLogo}
              />
              {companyInfo?.qualificationStatus?.value == 1 ? (
                <Image source={resIcon.iconVerify} style={homeStyle.vipIcon} />
              ) : null}
            </View>
            <View style={homeStyle.companyNameContainer}>
              <Text style={homeStyle.companyName} numberOfLines={2}>
                {companyInfo?.company}
                {'  '}
              </Text>
              <View style={homeStyle.unVerifiedBox}>
                <Text style={homeStyle.unVerified}>{I18n.t('page_company_text_unverified')}</Text>
              </View>
            </View>
          </View>
          <Image source={resIcon.homeArrow} />
        </Touchable>
      </LinearGradient>
    );
  };

  renderBalance = (homeStyle) => {
    const { balance } = this.props.companyStore;
    return (
      <>
        <Touchable
          style={homeStyle.balanceContainer}
          onPress={() => NavigationService.navigate('tradeRecord')}
        >
          <LinearGradient colors={['#4962A9', '#384A7C']} style={homeStyle.balanceBox}>
            <Text style={homeStyle.balanceTitle}>
              {I18n.t('page_home_text_balance_unit')}：
              <Text style={homeStyle.balanceAmount} textType="amount">
                {util.formatAmount(balance)}
              </Text>
            </Text>
            <Touchable style={homeStyle.balanceBtnBox} onPress={this.onRecharge}>
              <Text style={homeStyle.balanceBtn}>{I18n.t('page_home_text_recharge')}</Text>
            </Touchable>
          </LinearGradient>
        </Touchable>
        <View style={homeStyle.topContainer}>
          <Image source={resIcon.homeBaBg} style={{ width: '100%' }} />
        </View>
      </>
    );
  };

  render() {
    const { homeStyle } = this;
    const {
      statistics,
      homeResumeStatistics,
      totalCollection,
      totalWaitCount,
      totalPastCount,
      totalNotCount,
    } = this.props.companyStore;
    const ItemContent = this.renderSectionContent;
    return (
      <View style={homeStyle.container}>
        <ScrollView
          style={homeStyle.scorllViewContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={this.state.refreshing}
              onRefresh={this.onRefresh}
              tintColor="red"
              colors={['red']}
            />
          }
        >
          {this.renderTopHeader(homeStyle)}
          {this.renderBalance(homeStyle)}

          <ItemContent
            customSectionContainer={[
              homeStyle.sectionContainerFirst,
              homeStyle.sectionContainerExtra,
            ]}
            headerTitle={I18n.t('page_home_job_manage_title')}
            btnImg={resIcon.homeAdd}
            btnOnPress={() => NavigationService.navigate('jobAdd')}
            actions={[
              { value: statistics?.online, label: I18n.t('page_home_text_online'), type: 0 },
              { value: statistics?.pending, label: I18n.t('page_home_text_unpublished'), type: 1 },
              { value: statistics?.draft, label: I18n.t('page_home_text_draft'), type: 3 },
            ]}
            onPress={(item) => this.onJobManagerChange(item)}
          />
          <ItemContent
            customSectionContainer={[homeStyle.sectionContainerExtra]}
            headerTitle={I18n.t('page_resume_text_manager_title')}
            btnImg={resIcon.homeSearch}
            btnOnPress={() => NavigationService.navigate('resumeSearch')}
            actions={[
              {
                value: homeResumeStatistics?.chosenTotal || 0,
                label: I18n.t('page_home_text_pending_screening'),
                type: 0,
              },
              {
                value: homeResumeStatistics?.comunicationTotal || 0,
                label: I18n.t('page_home_text_pending_communication'),
                type: 1,
              },
              { value: totalCollection || 0, label: I18n.t('page_home_text_collected'), type: 99 },
            ]}
            onPress={(item) => this.onResume(item)}
          />

          <ItemContent
            customSectionContainer={[homeStyle.sectionContainerExtra]}
            headerTitle={I18n.t('page_home_interview_manage_title')}
            btnImg={null}
            actions={[
              {
                value: totalWaitCount,
                label: I18n.t('page_mine_interview_nav_title_interviewing'),
                type: 0,
              },
              {
                value: totalPastCount,
                label: I18n.t('page_mine_interview_nav_title_interviewed'),
                type: 1,
              },
              {
                value: totalNotCount,
                label: I18n.t('page_mine_interview_nav_title_not_interview'),
                type: 2,
              },
            ]}
            onPress={(item) => this.onInterview(item)}
          />
        </ScrollView>
        <CheckVersionModal />

        {/* 登录IM子账号弹出框 */}
        <SubImLoginModal ref={(ref) => (this.subImLoginModal = ref)} />
      </View>
    );
  }
}
