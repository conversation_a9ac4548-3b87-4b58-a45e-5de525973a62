import React from 'react';
import { Header, View, Alert, Touchable, Text, BaseComponent } from '../../../components';
import { inject, observer } from 'mobx-react';
import styles from '../../../themes/enterprise';
import I18n from '../../../i18n';
import NavigationService from '../../../navigationService';
import SettingItem from '../../../components/listItem/settingItem';
import DeviceInfo from '../../../util/deviceInfo';
import InputPwdModal from './components/inputPwdModal';
import SubImLoginModal from '../../../components/modal/SubImLoginModal';

/**
 * 设置
 */
@inject('loginAction', 'companyStore', 'stores')
@observer
export default class Setting extends BaseComponent {
  constructor(props) {
    super(props);
    this.mineStyle = styles.get('mine');
    this.state = {};
  }

  componentDidMount() {}

  onAccount = () => {};

  onModify = async () => {
    NavigationService.navigate('modifyPwd');
  };

  onChangeLanguage = () => {
    NavigationService.navigate('updateLanguage');
  };

  onLogout = async () => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('page_setting_sure_to_logout'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: async () => {
          this.props.loginAction.logoutForEp();
        },
      },
    ]);
  };

  onUnregister = async () => {
    Alert.alert(
      I18n.t('page_settings_unregister_alt_title'),
      I18n.t('page_settings_unregister_alt_content'),
      [
        {
          text: I18n.t('page_setting_cancel_text'),
          onPress: () => {},
        },
        {
          text: I18n.t('page_setting_confirm_text'),
          onPress: () => {
            this.pwdInputModal.show();
          },
        },
      ]
    );
  };

  onConfirm = async (pwd) => {
    try {
      this.showGlobalLoading();
      const { loginAction } = this.props;
      const res = await loginAction.unregisterForEnterPrise({
        captcha: '000000',
        password: pwd,
      });
      if (res?.data?.success) {
        this.showRequestResult(I18n.t('page_settings_unregister_op_success'));
        this.props.loginAction.logoutForEp();
      } else {
        this.showRequestResult(res?.message);
      }
    } catch (error) {
      this.showRequestResult(error?.message);
    }
  };

  onImAccount = () => {
    this.subImLoginModal.wrappedInstance.show();
  };

  render() {
    const { mineStyle } = this;
    const nowVersion = DeviceInfo.getVersion();
    const { companyInfo } = this.props.companyStore;
    const { companyStore } = this.props.stores;

    return (
      <View style={mineStyle.container}>
        <Header title={I18n.t('page_mine_switch_setting')} barStyle="dark-content" />

        <SettingItem
          label={I18n.t('page_setting_text_account_info')}
          onPress={this.onAccount}
          maxSpacing
          value={companyInfo?.name}
          hideRightArrow
          valueTextStyle={{ paddingRight: 0 }}
        />

        {/* 当前登录的IM账号入口 */}
        {companyStore.enableSubImAccount && (
          <SettingItem
            label={I18n.t('page_setting_text_current_im_account')}
            onPress={this.onImAccount}
            maxSpacing
            value={companyStore.selectedImAccount?.nickname || ''}
          />
        )}

        <SettingItem label={I18n.t('page_setting_text_modify_password')} onPress={this.onModify} />

        <SettingItem
          label={I18n.t('page_setting_text_language')}
          onPress={this.onChangeLanguage}
          maxSpacing
          value={I18n.t('language')}
        />
        <SettingItem
          label={I18n.t('page_setting_text_version')}
          hideRightArrow
          value={`V${nowVersion}`}
          valueTextStyle={{ paddingRight: 0 }}
        />
        {companyInfo?.name == 'lyta2017' ? (
          <SettingItem label={I18n.t('page_settings_unregister')} onPress={this.onUnregister} />
        ) : null}

        <Touchable onPress={this.onLogout} style={mineStyle.exitContainer}>
          <Text style={mineStyle.exitText}>{I18n.t('page_setting_exit_login')}</Text>
        </Touchable>

        <InputPwdModal ref={(ref) => (this.pwdInputModal = ref)} onConfirm={this.onConfirm} />

        {/* 切换登录子账号弹出框 */}
        <SubImLoginModal ref={(ref) => (this.subImLoginModal = ref)} />
      </View>
    );
  }
}
