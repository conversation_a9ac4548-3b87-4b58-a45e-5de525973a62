import React, { Component } from 'react';
import ExpandableText from 'rn-expandable-text';
import Toast from 'react-native-easy-toast';
import { ScrollView, View, Text, TouchableOpacity, Alert, Linking } from 'react-native';
import { Clipboard } from '../components';
import { <PERSON>er, Icon, Button, Badge } from 'react-native-elements';
import { inject, observer } from 'mobx-react';
import { toJS } from 'mobx';
import { headerStyle, baseBlueColor, subTitleColor, desColor, jobDetailStyle } from '../themes';
import Session from '../api/session';
import I18n from '../i18n';
import ResumeModal from '../components/resumeModal';
import LoadingModal from '../components/loadingModal';
import resIcon, { getAvatarSource, getEmployerAvatarSource } from '../res';
import Image from '../components/image';
import ImageBackground from '../components/imageBackground';
import NavigationService from '../navigationService';
import userAction from '../store/actions/user';
import sendMessageUtil from '../database/sendMessageUtil';
import constant from '../store/constant';
import chatSessionDao from '../database/dao/chatSessionDao';
import _ from 'lodash';
import LinearGradient from 'react-native-linear-gradient';
import JobDetailSkeleton from '../components/JobDetailSkeleton';
import jobService from '../api/jobService';
import Share from 'react-native-share';
import configs from '../configs';
import promiseUtil from '../util/promiseUtil';

@inject(
  'jobAction',
  'jobStore',
  'resumeStore',
  'resumeAction',
  'userAction',
  'loginAction',
  'companyAction',
  'userStore',
  'personStore'
)
@observer
export default class JobDetail extends Component {
  constructor(props) {
    super(props);
    const data = this.props.navigation.state.params;
    this.isPreview = data?.isPreview;
    this.isPush = data?.isPush;
    this.clickData = data?.clickData;
    this.state = {
      detail: data?.detail || {},
      loading: false,
      followed: false,
      msgSending: false,
      hideHandleBtn: false,
      showLoading: false,
      communicated: false,
      hasImAccount: 0, // 0: 初始状态，1: 有im账号，2: 没有im账号
    };
  }

  componentDidMount() {
    global.emitter.on(constant.event.jobFollowChange, this.onChangeFollowed);

    if (!this.isPreview) {
      Session.isLogin().then((isLogin) => {
        this.getDetails(isLogin);
      });
      // this.tryLoginIM();
      if (!this.isPush) {
        this.checkImAccount();
      }
    }
  }

  componentWillUnmount() {
    global.emitter.off(constant.event.jobFollowChange, this.onChangeFollowed);

    this.goBack();
  }

  onChangeFollowed = ({ jobId, followed }) => {
    if (this.state.detail.id === jobId) {
      this.setState({ followed });
    }
  };

  onPushJobClick = async () => {
    try {
      const { clickData } = this;
      await jobService.adJobClick(clickData);
    } catch (error) {
      console.warn('onPushJobClick', error);
    }
  };

  onShowAlert = () => {
    Alert.alert(I18n.t('page_setting_remind_text'), I18n.t('login_first_tips'), [
      {
        text: I18n.t('page_setting_cancel_text'),
        onPress: () => {},
      },
      {
        text: I18n.t('page_setting_confirm_text'),
        onPress: () => {
          this.props.navigation.navigate('login');
        },
      },
    ]);
  };

  onToCompanyDetail = async (detail) => {
    if (this.isPreview) return;
    this.props.navigation.navigate('companyDetail', {
      employerId: detail.employerId,
    });
  };

  onChatWithHr = async (resumeId) => {
    if (this.msgSending) return;
    this.msgSending = true;
    this.setState({ msgSending: true });
    try {
      const detail = toJS(this.state.detail);
      await this.props.jobAction.flagCommunicated(detail.id);
      detail.getTime = Date.now(); // 聊天消息刷新判断用
      if (__DEV__) {
        console.debug('onChatWithHr', detail);
      }
      let session = {
        sessionId: this.imInfo.im_id,
        title: this.imInfo.nickname,
        avatar: this.imInfo.avatar,
        company: this.imInfo.company,
        jobId: detail.id,
        jobTitle: detail.title,
        resumeId,
      };
      const localSession = await chatSessionDao.getChatSession({
        sessionId: session.sessionId,
      });
      const reqSendMsg =
        !localSession ||
        localSession.jobId !== session.jobId ||
        (localSession.resumeId ? localSession.resumeId !== session.resumeId : session.resumeId);
      // console.debug('jobDetail reqSendMsg', reqSendMsg, localSession, session);
      session = localSession ? _.merge(localSession, session) : session;
      if (reqSendMsg) {
        await sendMessageUtil.sendMessage({
          type: resumeId ? constant.messageType.resume : constant.messageType.job,
          content: '',
          sessionId: session.sessionId,
          session,
          localExtra: JSON.stringify({ job: detail, isFlagCommunicated: true }),
          jobTitle: detail.title,
          resumeId,
        });
      }
      this.msgSending = false;
      await promiseUtil.sleep(800);

      this.setState({ msgSending: false }, async () => {
        NavigationService.navigate('chatMessage', {
          session,
          sessionId: session.sessionId,
        });
      });
    } catch (e) {
      console.warn('onChatWithHr', e);
      this.msgSending = false;
      this.setState({ msgSending: false });
    }
  };

  getDetails = (isLogin) => {
    this.setState({ loading: true, showLoading: true });
    const { detail } = this.state;
    const { querySingleJobs, queryJob } = this.props.jobAction;
    const func = isLogin ? querySingleJobs : queryJob;
    func(detail?.id).then(
      async (res) => {
        if (this.isPush) {
          await this.checkImAccount(res.employerId);
          await this.onPushJobClick();
        }
        if (res && res.status && res.status.value === 2) {
          this.setState({ hideHandleBtn: true });
        } else {
          this.setState({ hideHandleBtn: false });
        }
        this.setState({
          detail: { ...detail, ...res },
          loading: false,
          showLoading: false,
          followed: res ? res.followed : false,
        });
      },
      () => {
        this.setState({ loading: false, showLoading: false });
      }
    );
  };

  onPhone = (phone) => {
    if (phone) {
      Linking.openURL(`tel:${phone}`);
    }
  };

  onCopy = (text) => {
    Clipboard.setString(text);
    global.toast.show(I18n.t('page_dynamic_text_copy_success'));
  };

  goBack = async () => {
    const { navigation } = this.props;
    const fuc = this.props.navigation.state.params;
    if (fuc && fuc.getFollowed) {
      await fuc.getFollowed();
      navigation.goBack();
      return;
    }
    navigation.goBack();
  };

  sendResume = (resumeId) => {
    if (resumeId) {
      this.toast.show(I18n.t('page_job_toast_send_resume_success'));
      this.props.jobAction.getJobStatistics();
      global.emitter.emit(constant.event.showResumeModal, {
        isOpen: false,
        page: 'jobDetail',
      });
      this.onChatWithHr(resumeId);
    } else {
      this.toast.show(I18n.t('page_job_toast_send_resume_fail'));
    }
  };

  toggleModal = () => {
    Session.isLogin().then((isLogin) => {
      if (isLogin) {
        const { hasDefaultResmue, defaultResmueId } = this.props.resumeStore;
        const { detail } = this.state;
        if (hasDefaultResmue) {
          this.setState({ showLoading: true });
          this.props.jobAction
            .sendResume(detail.id, {
              cvId: defaultResmueId,
            })
            .then(
              (res) => {
                this.setState({ showLoading: false });
                if (res.successful) {
                  this.toast.show(I18n.t('page_job_toast_send_resume_success'));
                  this.props.jobAction.getJobStatistics();
                  this.onChatWithHr(defaultResmueId);
                } else {
                  this.toast.show(res?.message || I18n.t('page_job_toast_send_resume_fail'));
                }
              },
              (err) => {
                this.setState({ showLoading: false });
                this.toast.show(err?.message || I18n.t('page_job_toast_send_resume_fail'));
              }
            );
        } else {
          global.emitter.emit(constant.event.showResumeModal, {
            isOpen: true,
            page: 'jobDetail',
          });
        }
      } else {
        this.onShowAlert();
      }
    });
  };

  followJob = () => {
    Session.isLogin().then(async (isLogin) => {
      if (isLogin) {
        const { detail } = this.state;
        if (!this.state.followed) {
          const result = await this.props.userAction.followJob(detail.id);
          if (result && result.message) {
            this.setState({ followed: true });
            this.toast.show(result.message);
            global.emitter.emit(constant.event.jobFollowChange, {
              jobId: detail.id,
              followed: true,
            });
          }
        } else {
          const result = await this.props.userAction.deleteFollowedJob({
            jobIds: [detail.id],
          });
          this.toast.show(result && result.message);
          if (result && result.successful) {
            this.setState({ followed: false });
            this.toast.show(result && result.message);
            global.emitter.emit(constant.event.jobFollowChange, {
              jobId: detail.id,
              followed: false,
            });
          }
        }
      } else {
        this.onShowAlert();
      }
    });
  };

  onShare = () => {
    const { detail } = this.state;
    const shareOptions = {
      title: detail?.title,
      message: `${I18n.t('page_job_recommendation')}\n${detail?.title}\n\n${
        configs.shareDownloadUrl
      }&jobId=${detail?.id}`,
      subject: detail?.title,
      //   failOnCancel: false,
    };
    if (IS_IOS) {
      shareOptions.url = `data:image/png;base64,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`;
      shareOptions.typ = 'image/png';
    }
    Share.open(shareOptions)
      .then((res) => {
        console.log('分享结果:', res);
        // this.toast.show(I18n.t('page_share_tips_text'));
      })
      .catch((error) => {
        console.warn('分享失败:', error);
      });
  };

  checkImAccount = async (employerId) => {
    try {
      const { detail } = this.state;
      if (!employerId && !detail?.employerId) return;
      this.imInfo = await userAction.getImInfo({
        accountType: 0,
        userId: detail?.employerId || employerId,
      });
      this.setState({ hasImAccount: this.imInfo ? 1 : 2 });
    } catch (e) {
      console.warn('jobDetail getImInfo', e);
      this.setState({ hasImAccount: 2 });
    }
  };

  renderPhone = (detail) => {
    const {
      personStore: { isLogin },
    } = this.props;
    const phone =
      (detail && detail.contact && detail.contact.telephone) ||
      (detail && detail.employer && detail.employer.regionCode && detail.employer.mobile
        ? detail.employer.regionCode + detail.employer.mobile
        : '');
    if (phone && isLogin) {
      return (
        <View style={jobDetailStyle.phoneContainer}>
          <Image
            style={{ width: 10, height: 16, marginHorizontal: 3.5 }}
            source={resIcon.jobPhone}
          />
          <Text
            numberOfLines={1}
            onPress={() => this.onPhone(phone)}
            style={jobDetailStyle.phoneText}
          >
            {phone}
          </Text>
        </View>
      );
    }
    return null;
  };

  renderEmail = (detail) => {
    // console.log("detail", detail);telephone
    const {
      personStore: { isLogin },
    } = this.props;
    const email =
      (detail && detail.contact && detail.contact.email) ||
      (detail && detail.employer && detail.email) ||
      '';
    if (email && isLogin) {
      return (
        <View style={jobDetailStyle.phoneContainer}>
          <Image style={{ width: 17, height: 13 }} source={resIcon.jobEmail} />
          <Text
            numberOfLines={1}
            onPress={() => this.onCopy(email)}
            style={jobDetailStyle.phoneText}
          >
            {email}
          </Text>
        </View>
      );
    }
    return null;
  };

  renderOnlineApple = (detail) => {
    const {
      personStore: { isLogin },
    } = this.props;
    const onlineApple =
      detail && detail.showAddress && detail.onlineApply ? detail.onlineApply : '';
    if (onlineApple && isLogin) {
      return (
        <View style={jobDetailStyle.phoneContainer}>
          <Text
            numberOfLines={1}
            onPress={() => this.onCopy(onlineApple)}
            style={[jobDetailStyle.phoneText, { marginLeft: 0 }]}
          >
            {onlineApple}
          </Text>
        </View>
      );
    }
    return null;
  };

  renderTag = (value, props = {}) => {
    if (!value) {
      return null;
    }
    return (
      <Badge
        value={value}
        containerStyle={jobDetailStyle.tagItem}
        badgeStyle={jobDetailStyle.tagBadgeStyle}
        textStyle={jobDetailStyle.tagText}
        {...props}
      />
    );
  };

  renderJobInfo = (detail) => (
    <View style={jobDetailStyle.jobCardCont}>
      <View style={[jobDetailStyle.cardTitleCont]}>
        <Text style={jobDetailStyle.cardTitle}>{detail ? detail.title : ''}</Text>
        <Text style={jobDetailStyle.cardWages}>
          {detail?.salaryId ? detail.salaryId.label : ''}
        </Text>
      </View>
      <View style={jobDetailStyle.tags}>
        {this.renderTag(detail?.jobLevelId && detail?.jobLevelId?.label)}
        {this.renderTag(
          detail?.workyears > 0 && `${detail?.workyears}${I18n.t('page_job_text_year')}`
        )}
        {this.renderTag(detail?.qualificationId && detail?.qualificationId?.label)}
        {this.renderTag(detail?.major, { textProps: { numberOfLines: 1 } })}
        {detail?.jobLangs && detail?.jobLangs.length > 0 ? (
          detail.jobLangs.map((lang, i) =>
            this.renderTag(`${lang.languageId.label}${lang.languageLevelId.label}`, {
              key: `${i + 1}`,
            })
          )
        ) : (
          <Text />
        )}
        {this.renderTag(detail?.sex && detail?.sex?.label)}
        {this.renderTag(detail?.ageTo > 0 && `${detail?.ageFrom}-${detail?.ageTo}`)}
        {this.renderTag(detail?.marital && detail?.marital.label)}
      </View>
      <View style={jobDetailStyle.spaceLine} />
      <View>
        <Text style={{ fontSize: 14, color: subTitleColor }}>
          {I18n.t('page_job_work_address')}
        </Text>
        <View style={{ flexDirection: 'row', marginTop: 8 }}>
          <Icon
            type="material"
            name="location-on"
            size={16}
            color={desColor}
            iconStyle={{ marginTop: 1 }}
          />
          {detail?.locations ? (
            <View style={jobDetailStyle.locationInfo}>
              {detail.locations.map((l, i) => (
                <View key={`${i + 1}`} style={jobDetailStyle.locationConf}>
                  <Text style={{ color: desColor, fontSize: 12 }}>
                    {l.locationId.label || l.address}
                  </Text>
                  {detail.locations.length > 1 &&
                  l.locationId.code !==
                    detail.locations[detail.locations.length - 1].locationId.code ? (
                    <Text style={jobDetailStyle.verticalLine} />
                  ) : (
                    <Text />
                  )}
                </View>
              ))}
            </View>
          ) : (
            <Text />
          )}
        </View>
      </View>
    </View>
  );

  renderCompanyInfo = (detail) => (
    <TouchableOpacity
      onPress={() => {
        this.onToCompanyDetail(detail);
      }}
    >
      <View style={jobDetailStyle.companyCardCont}>
        <ImageBackground
          imageStyle={{ borderRadius: 27 }}
          style={jobDetailStyle.companyLogo}
          source={getEmployerAvatarSource(detail?.employer ? detail.employer.logo : '')}
          defaultSource={resIcon.defaultEmployerAvatar}
          resizeMode="contain"
        >
          {detail &&
          detail.employer &&
          detail.employer.employerQualificationType &&
          detail.employer.employerQualificationType.value === 1 ? (
            <Image style={jobDetailStyle.v2} source={resIcon.verify} />
          ) : (
            <View />
          )}
        </ImageBackground>
        <View style={jobDetailStyle.companyContentInfo}>
          <Text style={jobDetailStyle.cardCompanyTitle}>
            {detail?.employer ? detail.employer.company : ''}{' '}
            {detail &&
            detail.employer &&
            detail.employer.qualificationStatus &&
            detail.employer.qualificationStatus.value === 1 ? (
              <Image source={resIcon.iconVerify} style={{ width: 9, height: 9 }} />
            ) : null}
          </Text>
          {detail &&
          detail.employer &&
          ((detail.employer.scaleId && detail.employer.scaleId.label) ||
            (detail.employer.industrialId && detail.employer.industrialId.label)) ? (
            <View style={jobDetailStyle.scaleIndustyContainer}>
              <Text style={jobDetailStyle.cardTag}>
                {detail.employer && detail.employer.scaleId.label
                  ? `${detail.employer.scaleId.label}${I18n.t('page_job_text_person')}`
                  : ''}
              </Text>
              {detail?.employer?.scaleId && detail.employer.scaleId?.label ? (
                <Text style={jobDetailStyle.verticalLine} />
              ) : (
                <Text style={{ display: 'none' }} />
              )}
              {detail?.employer?.industrialId ? (
                <Text style={jobDetailStyle.cardTag}>
                  {detail.employer.industrialId?.label
                    ? `${detail.employer.industrialId.label}`
                    : ''}
                </Text>
              ) : (
                <Text style={{ display: 'none' }} />
              )}
              {detail.employer?.companyType && detail.employer.companyType?.label ? (
                <Text style={jobDetailStyle.verticalLine} />
              ) : (
                <Text style={{ display: 'none' }} />
              )}
              <Text style={jobDetailStyle.cardTag}>
                {detail.employer?.companyType && detail.employer?.companyType?.label
                  ? `${detail.employer.companyType.label}`
                  : ''}
              </Text>
            </View>
          ) : (
            <Text style={{ display: 'none' }} />
          )}
          <View style={[jobDetailStyle.welfareCon, { flexShrink: 100 }]}>
            <Text style={[jobDetailStyle.cardTag, { lineHeight: 15 }]}>
              {I18n.t('page_job_company_welfare')}
            </Text>
            {detail?.employer && detail.employer?.welfare ? (
              <View style={jobDetailStyle.welfareContainer}>
                {detail.employer.welfare.map((item) => (
                  <View key={item} style={jobDetailStyle.welfareContainerItem}>
                    <Text style={jobDetailStyle.welfareContainerItemText}>{item}</Text>
                    {detail.employer.welfare.length > 1 &&
                    item !== detail.employer.welfare[detail.employer.welfare.length - 1] ? (
                      <Text style={jobDetailStyle.verticalLine} />
                    ) : (
                      <Text />
                    )}
                  </View>
                ))}
              </View>
            ) : (
              <Text style={[jobDetailStyle.welfareContainerItemText, { marginLeft: 6 }]}>
                {I18n.t('page_job_no_welfare')}
              </Text>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  renderCompetitivePower = (detail) => {
    return (
      <View style={[jobDetailStyle.jobDescCont]}>
        <Text style={jobDetailStyle.jobTitleDesc}>你与职位匹配情况</Text>
        <View style={jobDetailStyle.competitivePower}>
          <View style={jobDetailStyle.competitivePowerBox}>
            <LinearGradient
              colors={['#74cdfd', '#6da8f9']}
              style={jobDetailStyle.competitivePowerBar}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <View />
            </LinearGradient>

            <View style={jobDetailStyle.competitivePowerBarLine} />
            <LinearGradient
              colors={['#6da8f9', '#638bf7']}
              style={jobDetailStyle.competitivePowerBar}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <View />
            </LinearGradient>
            <View style={jobDetailStyle.competitivePowerBarLine} />
            <LinearGradient
              colors={['#638bf7', '#596ff6']}
              style={jobDetailStyle.competitivePowerBar}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            />
            <View style={jobDetailStyle.competitivePowerBarLine} />
            <LinearGradient
              colors={['#596ff6', '#4f5ff9']}
              style={jobDetailStyle.competitivePowerBar}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            />
          </View>
          <View style={jobDetailStyle.competitivePowerDesc}>
            <Text style={jobDetailStyle.competitivePowerDescText}>一般</Text>
            <Text style={jobDetailStyle.competitivePowerDescText}>良好</Text>
            <Text style={jobDetailStyle.competitivePowerDescText}>优秀</Text>
            <Text style={jobDetailStyle.competitivePowerDescText}>极好</Text>
          </View>
        </View>
      </View>
    );
  };

  renderWorkDesc = (detail) => (
    <View style={[jobDetailStyle.jobDescCont]}>
      <Text style={jobDetailStyle.jobTitleDesc}>{I18n.t('page_work_desc')}</Text>
      <ExpandableText
        numberOfLines={3}
        style={{
          fontSize: 14,
          marginLeft: 0,
          color: 'gray',
          textAlign: detail && detail.description ? 'left' : 'center',
        }}
        unexpandView={() => null}
        expandView={() =>
          detail && detail.description ? (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <View style={jobDetailStyle.lineView} />
              <View style={jobDetailStyle.moreText}>
                <Text style={jobDetailStyle.expandTitle}>{I18n.t('page_resume_label_more')}</Text>
                <Icon
                  name="chevron-small-down"
                  type="entypo"
                  size={24}
                  color={baseBlueColor}
                  iconStyle={{ marginTop: 4 }}
                />
              </View>
              <View style={jobDetailStyle.lineView} />
            </View>
          ) : (
            <Text style={{ display: 'none' }} />
          )
        }
      >
        {detail && detail.description ? detail.description : I18n.t('page_job_work_desc')}
      </ExpandableText>
    </View>
  );

  renderJobDesc = (detail) => (
    <View style={[jobDetailStyle.jobDescCont]}>
      <Text style={jobDetailStyle.jobTitleDesc}>{I18n.t('page_job_desc')}</Text>
      <ExpandableText
        numberOfLines={3}
        style={{
          fontSize: 14,
          marginLeft: 0,
          color: 'gray',
          textAlign: detail && detail.requirement ? 'left' : 'center',
        }}
        unexpandView={() => null}
        expandView={() =>
          detail && detail.requirement ? (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <View style={jobDetailStyle.lineView} />
              <View style={jobDetailStyle.moreText}>
                <Text style={jobDetailStyle.expandTitle}>{I18n.t('page_resume_label_more')}</Text>
                <Icon
                  name="chevron-small-down"
                  type="entypo"
                  size={24}
                  color={baseBlueColor}
                  iconStyle={{ marginTop: 4 }}
                />
              </View>
              <View style={jobDetailStyle.lineView} />
            </View>
          ) : (
            <Text style={{ display: 'none' }} />
          )
        }
      >
        {detail && detail.requirement ? detail.requirement : I18n.t('page_job_job_desc')}
      </ExpandableText>
    </View>
  );

  renderJobPublisher = (detail) => (
    <View style={[jobDetailStyle.jobDescCont, { marginBottom: 10 }]}>
      <Text style={jobDetailStyle.sendUser}>{I18n.t('page_job_publish_user')}</Text>
      <View style={jobDetailStyle.userContainer}>
        <View>
          <Image
            style={{ width: 54, height: 54, borderRadius: 27, backgroundColor: '#f5f5f5' }}
            source={getAvatarSource(detail && detail.contact ? detail.contact.avatar : '')}
            defaultSource={resIcon.defaultAvatar}
          />
        </View>
        <View style={{ marginLeft: 10, width: '80%' }}>
          <View style={{}}>
            <Text style={jobDetailStyle.userName}>
              {detail?.contact ? detail.contact?.name : ''}
            </Text>
            {detail?.contact && detail.contact?.positionId?.label ? (
              <Text style={jobDetailStyle.jobType}>{detail.contact.positionId.label}</Text>
            ) : (
              <Text style={{ display: 'none' }} />
            )}
          </View>
          {/* {this.renderPhone(detail)}
          {this.renderEmail(detail)} */}
          {/* {this.renderOnlineApple(detail)} */}
        </View>
      </View>
    </View>
  );

  renderButtonGroup = (detail) => {
    if (this.props.navigation.state.params?.isChat) return null;
    if (this.state.hasImAccount === 1) {
      return (
        <View style={[jobDetailStyle.btnContainer, { marginBottom: 5 }]}>
          <View
            style={{
              width: '50%',
              // (detail.communicated || this.state.communicated) && I18n.locale === 'en'
              //   ? '55%'
              //   : '50%',
            }}
          >
            <Button
              title={
                detail.communicated || this.state.communicated
                  ? I18n.t('page_job_btn_continue_chat')
                  : I18n.t('page_job_chat')
              }
              buttonStyle={jobDetailStyle.chatBtn}
              titleStyle={{ fontSize: 15, color: baseBlueColor }}
              onPress={this.onChatWithHr}
            />
          </View>
          <View
            style={{
              width: '50%',
              // (detail.communicated || this.state.communicated) && I18n.locale === 'en'
              //   ? '45%'
              //   : '50%',
            }}
          >
            <Button
              title={I18n.t('page_job_send')}
              buttonStyle={jobDetailStyle.sendBtn}
              titleStyle={{ fontSize: 15 }}
              onPress={this.toggleModal}
            />
          </View>
        </View>
      );
    }
    if (this.state.hasImAccount === 2) {
      return (
        <View style={jobDetailStyle.btnContainer}>
          <View style={{ width: '100%', paddingHorizontal: '2%' }}>
            <Button
              title={I18n.t('page_job_send')}
              buttonStyle={[jobDetailStyle.sendBtn, { width: '100%', marginLeft: 0 }]}
              titleStyle={{ fontSize: 15 }}
              onPress={() => {
                this.toggleModal();
              }}
            />
          </View>
        </View>
      );
    }
    return <View style={jobDetailStyle.btnContainer} />;
  };

  renderFavorite = () => {
    if (this.isPreview) return null;
    return (
      <View style={{ flexDirection: 'row' }}>
        <TouchableOpacity onPress={this.onShare} style={{ marginRight: 20 }}>
          <Icon type="font-awesome" name="share" size={20} color="white" />
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => {
            this.followJob();
          }}
        >
          <Icon
            type="font-awesome"
            name={this.state.followed ? 'star' : 'star-o'}
            color="#fff"
            size={21}
          />
        </TouchableOpacity>
      </View>
    );
  };

  renderBtn = (detail) => {
    if (this.isPreview) return null;
    return !this.state.hideHandleBtn ? (
      this.renderButtonGroup(detail)
    ) : (
      <Text style={jobDetailStyle.stop}>{I18n.t('page_job_detail_stop')}</Text>
    );
  };

  renderLeftIcon = () => (
    <TouchableOpacity onPress={this.goBack}>
      <Icon
        name="arrow-left"
        size={18}
        type="simple-line-icon"
        color="#fff"
        iconStyle={headerStyle.icon}
      />
    </TouchableOpacity>
  );

  render() {
    const { navigation } = this.props;
    const { detail, loading } = this.state;
    return (
      <View style={jobDetailStyle.jobContainer}>
        <Header
          statusBarProps={{
            barStyle: 'light-content',
            backgroundColor: '#2089DC',
          }}
          containerStyle={headerStyle.wrapper}
          centerComponent={{
            text: I18n.t('page_job_nav_job_detail'),
            style: headerStyle.center,
          }}
          leftComponent={this.renderLeftIcon()}
          rightComponent={this.renderFavorite}
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          {loading ? (
            <JobDetailSkeleton />
          ) : (
            <>
              {this.renderJobInfo(detail)}
              {this.renderCompanyInfo(detail)}
              {/* {this.renderCompetitivePower(detail)} */}
              {this.renderWorkDesc(detail)}
              {this.renderJobDesc(detail)}
              {this.renderJobPublisher(detail)}
            </>
          )}
        </ScrollView>
        {!loading ? this.renderBtn(detail) : <View />}
        <ResumeModal
          getResumeId={false}
          nav={navigation}
          jobId={detail?.id}
          sendResume={this.sendResume}
          page="jobDetail"
        />
        <Toast
          ref={(ref) => {
            this.toast = ref;
          }}
          position="center"
        />
        <LoadingModal isOpen={this.state.msgSending} loadingTips={false} />
        <LoadingModal isOpen={this.state.showLoading} loadingTips={false} />
      </View>
    );
  }
}
